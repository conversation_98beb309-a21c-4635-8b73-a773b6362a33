import React from 'react';
import { WeakTokenProvider } from '../../types/TokenProvider';
import {
  UseGraphApiError,
  initGraphClient,
} from './useGraphApiAccessor';

export interface IUserChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  chatType?: 'oneOnOne' | 'group' | 'meeting' |'TeamsChannel'; // Microsoft Graph APIのchatType
}

export interface IUserChat {
  id: string;
  topic: string | null;
  chatType: string;
  members?: {
    displayName: string;
    id: string;
  }[];
}

export interface IUserTeam {
  id: string;
  displayName: string;
  channels?: IUserChannel[];
}

export interface IUserChannel {
  id: string;
  displayName: string;
  membershipType: string;
}

export type FetchUserChatsAndChannels = () => Promise<IUserChatItem[]>;

export type UseUserChatsAndChannelsReturnType = {
  fetchUserChatsAndChannels: FetchUserChatsAndChannels | undefined;
  isLoading: boolean;
  error: string | null;
}

/**
 * ユーザーが参加しているチャットの一覧を取得する
 */
async function fetchUserChatsImpl(
  tokenProvider: WeakTokenProvider,
): Promise<IUserChat[]> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  try {
    const response = await client
      .api('/me/chats')
      .expand('members')
      .get();

    return response.value || [];
  } catch (error) {
    // TODO:エラーハンドリング改善
    console.error('Error fetching user chats:', error);
    return [];
  }
}

/**
 * ユーザーが参加しているチームとチャネルの一覧を取得する
 */
async function fetchUserTeamsAndChannelsImpl(
  tokenProvider: WeakTokenProvider,
): Promise<IUserTeam[]> {
  const client = initGraphClient(tokenProvider);
  if (!client) return Promise.reject(new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE));

  try {
    // まずユーザーが参加しているチームを取得
    const teamsResponse = await client
      .api('/me/joinedTeams')
      .get();

    const teams: IUserTeam[] = teamsResponse.value || [];

    // 各チームのチャネルを取得
    const teamsWithChannels = await Promise.all(
      teams.map(async (team) => {
        try {
          const channelsResponse = await client
            .api(`/teams/${team.id}/channels`)
            .get();
          return { ...team, channels: channelsResponse.value || [] };
        } catch (error) {
          return { ...team, channels: [] };
        }
      }),
    );

    return teamsWithChannels;
  } catch (error) {
    console.error('Error fetching user teams and channels:', error);
    return [];
  }
}

/**
 * チャットとチャネルを統合してIUserChatItem形式に変換する
 */
function convertToUserChatItems(
  chats: IUserChat[],
  teams: IUserTeam[],
): IUserChatItem[] {
  const items: IUserChatItem[] = [];

  // チャットを追加
  chats.forEach((chat) => {
    let name = chat.topic || 'チャット';

    // トピックがない場合はメンバー名を使用
    if (!chat.topic && chat.members && chat.members.length > 0) {
      const memberNames = chat.members
        .map((member) => member.displayName)
        .filter((memberName) => memberName)
        .join(', ');
      name = memberNames || 'チャット';
    }

    items.push({
      id: chat.id,
      name,
      type: 'チャット',
      chatType: chat.chatType as 'oneOnOne' | 'group' | 'meeting', // Microsoft Graph APIのchatTypeを保持
    });
  });

  // チャネルを追加
  teams.forEach((team) => {
    if (team.channels) {
      team.channels.forEach((channel) => {
        items.push({
          id: channel.id,
          name: `${team.displayName} - ${channel.displayName}`,
          type: 'チャネル',
          // チャネルのchatTypeはTeamsChannelで固定
          chatType: 'TeamsChannel',
        });
      });
    }
  });

  return items;
}

/**
 * ユーザーのチャットとチャネルを取得するカスタムフック
 */
const useUserChatsAndChannelsAccessor = (
  tokenProvider: WeakTokenProvider,
): UseUserChatsAndChannelsReturnType => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const fetchUserChatsAndChannels = React.useCallback(async (): Promise<IUserChatItem[]> => {
    if (!tokenProvider) {
      throw new Error(UseGraphApiError.TOKEN_PROVIDER_NOT_AVAILABLE);
    }

    setIsLoading(true);
    setError(null);

    try {
      // チャットとチームを並行して取得
      const [chats, teams] = await Promise.all([
        fetchUserChatsImpl(tokenProvider),
        fetchUserTeamsAndChannelsImpl(tokenProvider),
      ]);

      // 統合してIUserChatItem形式に変換
      // この時chatsのchatTypeはAPIから返ってきたものをそもまま当てはめる
      // teamsのchatTypeは一つしかない
      // まずモーダルからfetchUserChatsAndChannelsでデータを取得しに行って
      // teamsとchat両方データを取りに行って、chatTypeとIdをconvertToUserChatItemsが割り振る
      // このitemsが表示される
      // この次にitemsたちがちゃんとchatTypeを保持していて、保存されているか確認する必要がある。
      const items = convertToUserChatItems(chats, teams);

      setIsLoading(false);
      return items;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setIsLoading(false);
      throw err;
    }
  }, [tokenProvider]);

  return {
    fetchUserChatsAndChannels: tokenProvider ? fetchUserChatsAndChannels : undefined,
    isLoading,
    error,
  };
};

export default useUserChatsAndChannelsAccessor;
