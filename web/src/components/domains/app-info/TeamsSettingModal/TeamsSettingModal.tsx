import * as React from 'react';
import {
  <PERSON><PERSON>, Header, Input,
} from '@fluentui/react-northstar';
import { AddIcon, AcceptIcon, CloseIcon } from '@fluentui/react-icons-northstar';
import { mergedClassName } from '../../../../utilities/commonFunction';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import useUserChatsAndChannelsAccessor, { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../../../../hooks/utilities/useComponentInitUtility';
import TeamsSettingTabs, { TeamsSettingTabType, TeamsSettingTabTypeValue } from './TeamsSettingTabs';
import SelectedItemsList from './SelectedItemsList';

// CSS
import './TeamsSettingModal.scss';
import { UseTeamsChatsApiReturnType } from '../../../../hooks/accessors/useTeamsChatsApiAccessor';

// チャットアイテムの型定義
export interface IChatItem {
  id: string;
  name: string;
  type: 'チャット' | 'チャネル';
  isBookmarked: boolean;
}

export interface ISimpleModalProps {
  className?: string;
  open?: boolean;
  onClose: () => void;
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType,
  // userId?: string; // ユーザーIDの登録も必要かも
}

/**
 * TeamsSettingModal
 * @param props
 */
const TeamsSettingModal: React.FC<ISimpleModalProps> = (props) => {
  const {
    className,
    open,
    onClose,
    useTeamsChatsApiAccessorReturn,
  } = props;

  const { postTeamsChatsApi } = useTeamsChatsApiAccessorReturn;
  // postTeamsChatsApiはrequestをとり、requestの中身は
  //   export interface ITeamsChatsRequest {
  //   // チャットorchannelId
  //   chatOrChannelId?: string;
  //   // これ不明
  //   chatType: 'チャット' | 'チャネル';
  // }である。
  // TODO:次にやるべきは何を渡すべきなのかはっきりさせてITeamsChatsRequestを確定してから
  // ここのファイルでそれらを渡してやる

  /**
   * ラベル
   */
  const TeamsSettingLabel = {
    TITLE: 'Teams設定',
  };

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });
  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    fetchUserChatsAndChannels,
    isLoading, error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // 状態管理
  const [searchQuery, setSearchQuery] = React.useState('');
  const [allChatItems, setAllChatItems] = React.useState<IUserChatItem[]>([]);
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = React.useState<TeamsSettingTabTypeValue>(
    TeamsSettingTabType.CHAT,
  );

  // マージされたCSSクラス名
  const rootClassName = React.useMemo(() => {
    const step1 = mergedClassName('simple-modal', className);
    const isOpen = open ? 'is-open' : '';
    const hasSelectedItems = selectedItems.size > 0 ? 'has-selected-items' : '';
    return mergedClassName(mergedClassName(isOpen, step1), hasSelectedItems);
  }, [className, open, selectedItems.size]);

  // データ取得のEffect
  React.useEffect(() => {
    if (open && fetchUserChatsAndChannels) {
      fetchUserChatsAndChannels()
        .then((items) => {
          setAllChatItems(items);
          setFilteredChatItems(items);
        })
        .catch((err) => {
          // エラーを再スローして上位でハンドリングできるようにする
          throw err;
        });
    }
  }, [open, fetchUserChatsAndChannels]);

  // 検索フィルタリングのEffect（タブとテキスト検索の両方に対応）
  React.useEffect(() => {
    let filtered = allChatItems;

    // タブによるフィルタリング
    filtered = filtered.filter((item) => item.type === activeTab);

    // テキスト検索によるフィルタリング
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) => item.id.toLowerCase().includes(searchQuery.toLowerCase())
        || item.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredChatItems(filtered);
  }, [searchQuery, allChatItems, activeTab]);

  const handleClose = React.useCallback(() => {
    if (onClose) onClose();
  }, [onClose]);

  // 検索クエリ入力の変更ハンドラー
  const handleSearchQueryChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setSearchQuery(data?.value ?? '');
    },
    [],
  );

  // タブ変更ハンドラー
  const handleTabChange = React.useCallback((tab: TeamsSettingTabTypeValue) => {
    setActiveTab(tab);
    // タブ切り替え時に検索クエリをクリア
    setSearchQuery('');
  }, []);

  // チャットとチャネルの件数を計算
  const chatCount = React.useMemo(() => allChatItems.filter((item) => item.type
  === TeamsSettingTabType.CHAT).length, [allChatItems]);

  const channelCount = React.useMemo(() => allChatItems.filter((item) => item.type
  === TeamsSettingTabType.CHANNEL).length, [allChatItems]);

  // プレースホルダーテキストをタブに応じて変更
  const searchPlaceholder = React.useMemo(() => (activeTab === TeamsSettingTabType.CHAT ? 'チャット名で検索' : 'チャネル名で検索'), [activeTab]);

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  // 選択されたアイテムを削除するハンドラー
  const handleRemoveSelectedItem = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, id: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(id);
    }
  }, [handleItemToggle]);

  // 保存ハンドラー
  const handleSave = React.useCallback(async () => {
    if (!postTeamsChatsApi || selectedItems.size === 0) return;

    // 選択されたアイテムを取得してAPIリクエストを作成
    const selectedChatItems = allChatItems.filter((item) => selectedItems.has(item.id));

    // 各選択されたアイテムに対してAPIを呼び出し
    await Promise.all(selectedChatItems.map(async (item) => {
      const request = {
        itemId: item.id,
        chatId: item.type === 'チャット' ? item.id : undefined,
        channelId: item.type === 'チャネル' ? item.id : undefined,
        chatType: item.type,
        graphChatType: item.chatType, // Microsoft Graph APIのchatType
      };

      await postTeamsChatsApi(request);
    }));

    // 保存成功後にモーダルを閉じる
    handleClose();
  }, [postTeamsChatsApi, selectedItems, allChatItems, handleClose]);

  return (
    <div className={rootClassName}>
      {/* SP用閉じるボタン */}
      <div className="simple-modal-edge">
        <ModalCardTop
          showBookmark={false}
          onClickClose={handleClose}
        />
      </div>

      {/* PC用閉じるボタン */}
      <div className="simple-modal-close-pc">
        <Button
          className="simple-modal-close-pc-button"
          icon={<CloseIcon />}
          text
          iconOnly
          onClick={handleClose}
        />
      </div>

      <div className="simple-modal-scroll-wrapper">
        <div className="simple-modal-scroll-inner">
          <div className="simple-modal-header">
            <Header content={TeamsSettingLabel.TITLE} as="h3" className="simple-modal-title" />
          </div>
          <div className="simple-modal-content">
            <p>検索対象を選択できます。</p>

            {/* タブ切り替え */}
            <TeamsSettingTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
              disabled={isLoading}
              chatCount={chatCount}
              channelCount={channelCount}
            />

            {/* 検索フィールド */}
            <div className="simple-modal-chat-input">
              <Input
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={handleSearchQueryChange}
                fluid
              />
            </div>

            {/* 選択されたアイテム一覧 */}
            <SelectedItemsList
              selectedItems={selectedItems}
              allChatItems={allChatItems}
              onRemoveItem={handleRemoveSelectedItem}
            />

            {/* チャットアイテム一覧 */}
            <div className="simple-modal-chat-items">
              {isLoading && (
                <div className="simple-modal-loading">
                  <p>チャットとチャネルを読み込み中...</p>
                </div>
              )}
              {error && (
                <div className="simple-modal-error">
                  <p>
                    エラーが発生しました:
                    {error}
                  </p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.length === 0 && (
                <div className="simple-modal-no-results">
                  <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
                </div>
              )}
              {!isLoading && !error && filteredChatItems.map((item) => {
                const isSelected = selectedItems.has(item.id);
                const itemClassName = `simple-modal-chat-item${isSelected ? ' selected' : ''}`;
                return (
                  <div
                    key={item.id}
                    className={itemClassName}
                    onClick={() => handleItemToggle(item.id)}
                    onKeyDown={(event) => handleKeyDown(event, item.id)}
                    role="button"
                    tabIndex={0}
                    aria-pressed={isSelected}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className="simple-modal-chat-item-content">
                      <span className="simple-modal-chat-item-name">{item.name}</span>
                    </div>
                    {isSelected ? (
                      <AcceptIcon
                        style={{
                          color: 'var(--color-guide-brand-icon)',
                          fontSize: '20px',
                          transform: 'scale(1.1)',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    ) : (
                      <AddIcon
                        style={{
                          color: 'var(--color-guide-foreground-2)',
                          fontSize: '20px',
                          transition: 'transform 0.2s ease, color 0.2s ease',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>

            {/* 保存ボタン */}
            <div className="simple-modal-save-section" style={{ marginTop: '20px', padding: '0 20px' }}>
              <Button
                primary
                content="保存"
                disabled={selectedItems.size === 0 || !postTeamsChatsApi}
                onClick={handleSave}
                fluid
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

TeamsSettingModal.defaultProps = {
  className: '',
  open: false,
  // userId: undefined,
};

export default TeamsSettingModal;
