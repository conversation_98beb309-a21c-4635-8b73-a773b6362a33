@import '../../../../styles/variables';

.selected-items-list {
  margin: 8px 0 12px 0;
  padding: 6px 0;
  background-color: transparent;
  max-height: 44px;
  overflow: hidden;
}

.selected-items-header {
  margin-bottom: 8px;
}

.selected-items-title {
  font-size: 12px;
  color: var(--color-guide-foreground-2);
  font-weight: 500;
}

.selected-items-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 6px;
  overflow-x: auto;
  padding-bottom: 4px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  // スクロールバーのスタイリング（Webkit系ブラウザ）
  &::-webkit-scrollbar {
    height: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-guide-foreground-5);
    border-radius: 1px;

    &:hover {
      background: var(--color-guide-foreground-4);
    }
  }

  // Firefox用のスクロールバースタイリング
  scrollbar-width: thin;
  scrollbar-color: var(--color-guide-foreground-4) rgba(0, 0, 0, 0.1);
}

.selected-item {
  display: flex;
  align-items: center;
  background-color: var(--color-guide-foreground-6);
  border-radius: 10px;
  padding: 3px 6px 3px 8px;
  font-size: 11px;
  color: var(--color-guide-foreground-1);
  min-width: 100px;
  max-width: 220px;
  flex-shrink: 0;
  height: 24px;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--color-guide-foreground-5);
  }
}

.selected-item-type {
  font-size: 9px;
  color: var(--color-guide-foreground-2);
  margin-right: 3px;
  flex-shrink: 0;
}

.selected-item-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 4px;
  min-width: 0;
}

.selected-item-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 1px;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  color: var(--color-guide-foreground-2);
  transition: background-color 0.2s, color 0.2s;
  flex-shrink: 0;

  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--color-guide-foreground-1);
  }

  &:focus {
    outline: 1px solid var(--color-guide-brand-main-foreground);
    outline-offset: 1px;
  }

  svg {
    width: 8px;
    height: 8px;
  }
}

/* ダークテーマ対応 */
@media (prefers-color-scheme: dark) {
  .selected-items-list {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .selected-item {
    &:hover {
      background-color: var(--color-guide-foreground-4);
    }
  }

  .selected-item-remove {
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

/* 空の状態では表示しない */
.selected-items-list:empty {
  display: none;
}