import * as React from 'react';
import { CloseIcon } from '@fluentui/react-icons-northstar';
import { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// CSS
import './SelectedItemsList.scss';

export interface ISelectedItemsListProps {
  selectedItems: Set<string>;
  allChatItems: IUserChatItem[];
  onRemoveItem: (id: string) => void;
}

/**
 * SelectedItemsList
 * 選択されたチャット・チャネルアイテムを表示するコンポーネント
 */
const SelectedItemsList: React.FC<ISelectedItemsListProps> = (props) => {
  const {
    selectedItems,
    allChatItems,
    onRemoveItem,
  } = props;

  // 選択されたアイテムの詳細情報を取得
  const selectedItemDetails = React.useMemo(() => Array.from(selectedItems)
    .map((id) => allChatItems.find((item) => item.id === id))
    .filter((item): item is IUserChatItem => item !== undefined), [selectedItems, allChatItems]);

  // 選択されたアイテムがない場合は何も表示しない
  if (selectedItemDetails.length === 0) {
    return null;
  }

  return (
    <div className="selected-items-list">
      <div className="selected-items-container">
        {selectedItemDetails.map((item) => (
          <div key={item.id} className="selected-item">
            <span className="selected-item-type">{item.type}</span>
            <span className="selected-item-name">{item.name}</span>
            <button
              type="button"
              className="selected-item-remove"
              onClick={() => onRemoveItem(item.id)}
              aria-label={`${item.name}の選択を解除`}
              title={`${item.name}の選択を解除`}
            >
              <CloseIcon />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectedItemsList;
